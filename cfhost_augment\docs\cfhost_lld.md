# 像素引擎宿主-插件框架详细设计文档 (LLD)

## 1. 项目结构与总体设计

### 1.1 目录结构树 (Directory Tree)

```
f:\Project\cfpixelengine\cfhost
├── cfhost.exe             # 宿主程序主入口
├── host/                  # 宿主核心逻辑
│   ├── HostApplication.h    # 宿主应用类，管理生命周期
│   ├── HostApplication.cpp
│   ├── PluginManager.h      # 插件加载与管理
│   ├── PluginManager.cpp
│   ├── ServiceManager.h     # 服务注册与管理
│   └── ServiceManager.cpp
├── interfaces/            # 插件与服务需实现的接口
│   ├── IPlugin.h          # 插件接口
│   ├── IService.h         # 所有服务的基础接口
│   └── IServiceProvider.h # 服务提供者接口
├── services/              # 框架提供的服务实现
│   ├── LogService.h         # 日志服务
│   └── LogService.cpp
├── plugin/                # 插件相关目录
│   └── config/            # 存放插件配置文件
│       └── idphoto.json     # 示例插件配置
└── idphoto.dll            # 示例插件动态库
```

### 1.2 整体逻辑和交互时序图

核心流程是：宿主启动，解析命令行获得插件ID，然后通过`PluginManager`加载插件配置和DLL。同时，`ServiceManager`按需创建服务并构建一个`ServiceProvider`。接着，通过`setServiceProvider`方法将提供者注入插件，最后依次调用插件的生命周期方法。

```mermaid
sequenceDiagram
    participant C as 命令行
    participant H as cfhost.exe
    participant PM as host/PluginManager
    participant SM as host/ServiceManager
    participant SP as interfaces/IServiceProvider
    participant P as idphoto.dll (IPlugin)

    C->>H: 启动 --plugin-id idphoto
    H->>H: 解析命令行参数
    H->>PM: loadPlugin("idphoto")
    PM->>PM: 读取 plugin/config/idphoto.json
    PM->>SM: createServiceProviderForPlugin(pluginConfig)
    SM->>SP: new ServiceProvider()
    Note right of SM: 注册基础服务(LogService)到Provider
    Note right of SM: 按需查找并注册可选服务到Provider
    SM-->>PM: 返回 ServiceProvider 实例
    PM->>PM: 加载 idphoto.dll
    PM->>P: create instance
    PM->>P: setServiceProvider(serviceProvider)
    PM->>P: initialize()
    P->>SP: getService<ILogService>()
    SP-->>P: 返回 ILogService* 实例
    PM->>P: getWidget()
    P-->>PM: 返回 QWidget*
    PM-->>H: 返回插件实例和UI
    H->>H: 显示插件UI，进入事件循环
    ...用户操作...
    H->>P: shutdown()
    H->>H: 进程退出
```

## 2. 数据实体结构深化

### 2.1 插件配置文件 (JSON)

插件的核心元数据，定义了插件的基本信息和依赖。

```json
{
    "baseInfo": {
        "appId": "idphoto",
        "appName": "证件照",
        "buildVersion": 1
    },
    "runEnvInfo": {
        "appClassify": {
            "appType": "plugin",
            "dependService": [
                "ocrService"
            ],
            "launcherDll": "idphoto.dll",
            "launcherType": "native"
        }
    }
}
```

### 2.2 实体关系图

由于当前只有一种核心实体（插件配置），ER图相对简单，主要展示宿主、插件与服务之间的“提供”与“消费”关系。

```mermaid
erDiagram
    HOST ||--o{ PLUGIN : 加载
    HOST ||--o{ SERVICE : 提供
    PLUGIN }o--|| SERVICE : 消费

    HOST {
        string name
    }
    PLUGIN {
        string appId PK
        string launcherDll
        string[] dependService
    }
    SERVICE {
        string serviceName PK
    }
```

## 3. 模块化文件详解

### 3.1 `interfaces/IPlugin.h`

a. **文件用途说明**
   定义了所有插件必须实现的C++接口。通过`setServiceProvider`注入服务，插件内部通过`m_serviceProvider`成员访问服务。

b. **文件内类图**
```mermaid
classDiagram
    class IServiceProvider{
        <<interface>>
    }
    class IPlugin{
        <<interface>>
        #m_serviceProvider: IServiceProvider*
        +setServiceProvider(IServiceProvider* provider)
        +initialize(): bool
        +getWidget(): QWidget*
        +shutdown(): void
    }
    IPlugin o-- IServiceProvider
```

c. **函数/方法详解**

#### `setServiceProvider(IServiceProvider* provider)`
- **用途:** 由宿主调用，用于向插件实例注入服务提供者。
- **说明:** 此方法在`IPlugin`接口中提供默认实现，插件无需重写。默认实现会将传入的`provider`保存在`m_serviceProvider`成员中。
- **输入参数:**
  - `provider`: `IServiceProvider*`，服务提供者实例。
- **输出数据结构:** 无。

#### `initialize()`
- **用途:** 执行插件的初始化逻辑。此时服务已注入，可通过`m_serviceProvider`获取。
- **输入参数:** 无。
- **输出数据结构:** `bool`，`true`表示初始化成功，`false`表示失败。
- **实现流程:**
```mermaid
flowchart TD
    A[开始] --> B{使用 m_serviceProvider->getService<T>() 获取所需服务};
    B --> C{关键服务是否存在?};
    C -- 是 --> D[进行插件内部初始化];
    D --> E{初始化是否成功?};
    E -- 是 --> F[返回 true];
    E -- 否 --> G[返回 false];
    C -- 否 --> G;
    F --> Z[结束];
    G --> Z;
```

#### `getWidget()`
- **用途:** 创建并返回插件的主用户界面。
- **输入参数:** 无。
- **输出数据结构:** `QWidget*`，指向插件主UI控件的指针。

#### `shutdown()`
- **用途:** 在应用关闭前执行清理工作。
- **输入参数:** 无。
- **输出数据结构:** 无。

### 3.2 `interfaces/IServiceProvider.h`

a. **文件用途说明**
   定义了服务提供者接口。插件通过它的模板方法`getService`，以类型安全的方式获取服务实例。

b. **文件内类图**
```mermaid
classDiagram
    class IService{
        <<interface>>
    }
    class IServiceProvider{
        <<interface>>
        +getService<T>(): T*
    }
    IServiceProvider ..> IService : returns
```

c. **函数/方法详解**

#### `getService<T>()`
- **用途:** 根据模板参数中指定的接口类型，获取相应的服务实例。
- **输入参数:** 无（类型通过模板参数`T`传递）。
- **输出数据结构:** `T*`，返回一个指向服务接口的指针。如果服务未注册或类型不匹配，则返回`nullptr`。例如 `ILogService* logger = provider->getService<ILogService>();`。

### 3.3 `host/PluginManager.h` & `host/PluginManager.cpp`

a. **文件用途说明**
   负责查找、解析、加载和管理插件的完整生命周期。

b. **文件内类图**
```mermaid
classDiagram
    class ServiceManager
    class IPlugin
    class PluginManager {
        -ServiceManager* m_serviceManager
        +loadPlugin(string pluginId): IPlugin*
        +unloadPlugin(IPlugin* plugin)
    }
    PluginManager o-- ServiceManager
    PluginManager ..> IPlugin
```

c. **函数/方法详解**

#### `loadPlugin(string pluginId)`
- **用途:** 完成从插件ID到插件实例加载的全过程。
- **实现流程:**
```mermaid
sequenceDiagram
    participant C as Caller
    participant PM as PluginManager
    participant SM as ServiceManager
    participant P as IPlugin

    C->>PM: loadPlugin("idphoto")
    PM->>PM: 定位并解析 plugin/config/idphoto.json
    alt 配置文件或DLL无效
        PM->>PM: LogError("配置错误")
        PM-->>C: return nullptr
    end
    PM->>SM: createServiceProviderForPlugin(config)
    SM-->>PM: return serviceProvider
    PM->>PM: QPluginLoader加载 idphoto.dll
    PM->>P: qobject_cast<IPlugin*>()
    alt 加载或转换失败
        PM->>PM: LogError("DLL加载失败")
        PM-->>C: return nullptr
    end
    PM->>P: initialize(serviceProvider)
    alt 初始化失败
        PM->>P: LogError("插件初始化失败")
        PM-->>C: return nullptr
    end
    PM-->>C: return pluginInstance
```

### 3.4 `host/ServiceManager.h` & `host/ServiceManager.cpp`

a. **文件用途说明**
   负责按需创建服务实例，并为插件构建专属的服务提供者。

b. **文件内类图**
```mermaid
classDiagram
    class IService
    class IServiceProvider
    class ServiceManager {
        -map<string, function<IService*()>> m_serviceFactories
        +registerServiceFactory(string name, function<IService*()> factory)
        +createServiceProviderForPlugin(config): IServiceProvider*
    }
    ServiceManager ..> IService
    ServiceManager ..> IServiceProvider
```

c. **函数/方法详解**

#### `createServiceProviderForPlugin(config)`
- **用途:** 为特定插件实例创建一个专属的服务提供者，并按需实例化其依赖的服务。
- **实现流程:**
```mermaid
flowchart TD
    A[开始] --> B(创建空的 ServiceProvider 实例);
    B --> C{注册所有基础服务到Provider};
    C --> D{遍历插件config中的dependService列表};
    D -- 对每个依赖的服务名 --> E{在m_serviceFactories中查找对应的服务工厂};
    E -- 找到工厂 --> F(调用工厂函数创建服务实例);
    F --> G{创建成功?};
    G -- 是 --> H(将新创建的服务实例注册到Provider);
    G -- 否 --> I(LogError, 返回nullptr);
    E -- 未找到 --> J(LogError, 返回nullptr);
    D -- 遍历完成 --> K(返回配置好的Provider实例);
```

## 4. 迭代演进依据

1.  **服务与插件解耦:** 通过`IServiceProvider`作为中间层，插件不直接依赖任何具体服务实现，未来替换或升级服务对插件是透明的。
2.  **配置驱动加载:** 插件和其依赖均通过配置文件声明，未来新增插件或服务，无需修改宿主核心代码，只需添加新的插件包和配置文件即可。
3.  **接口稳定:** `IPlugin`和`IServiceProvider`接口足够抽象和稳定，未来的功能扩展（如新增服务）可通过此框架平滑进行，而无需破坏现有接口。
