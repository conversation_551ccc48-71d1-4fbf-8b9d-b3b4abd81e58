#pragma once

#include <QString>
#include "IService.h"

/**
 * @brief 日志服务接口
 * 
 * 提供统一的日志记录功能，支持不同级别的日志输出。
 * 这是一个基础服务，所有插件都可以使用。
 */
class ILogService : public IService
{
public:
    virtual ~ILogService() = default;

    /**
     * @brief 记录信息级别日志
     * @param message 日志消息
     */
    virtual void info(const QString& message) = 0;

    /**
     * @brief 记录警告级别日志
     * @param message 日志消息
     */
    virtual void warning(const QString& message) = 0;

    /**
     * @brief 记录错误级别日志
     * @param message 日志消息
     */
    virtual void error(const QString& message) = 0;

    /**
     * @brief 记录调试级别日志
     * @param message 日志消息
     */
    virtual void debug(const QString& message) = 0;
};
