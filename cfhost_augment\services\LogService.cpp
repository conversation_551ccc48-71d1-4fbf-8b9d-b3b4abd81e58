#include "LogService.h"
#include <QDebug>
#include <QDateTime>

LogService::LogService()
{
    // 构造函数，当前无需特殊初始化
}

void LogService::info(const QString& message)
{
    QString formattedMessage = formatMessage("INFO", message);
    qDebug() << formattedMessage;
}

void LogService::warning(const QString& message)
{
    QString formattedMessage = formatMessage("WARN", message);
    qWarning() << formattedMessage;
}

void LogService::error(const QString& message)
{
    QString formattedMessage = formatMessage("ERROR", message);
    qCritical() << formattedMessage;
}

void LogService::debug(const QString& message)
{
    QString formattedMessage = formatMessage("DEBUG", message);
    qDebug() << formattedMessage;
}

QString LogService::formatMessage(const QString& level, const QString& message) const
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    return QString("[%1] [%2] %3").arg(timestamp, level, message);
}
