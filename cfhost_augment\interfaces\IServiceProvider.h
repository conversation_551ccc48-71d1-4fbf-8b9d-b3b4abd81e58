#pragma once

#include <typeinfo>
#include <type_traits>
#include "IService.h"

/**
 * @brief 服务提供者接口
 * 
 * 插件通过此接口以类型安全的方式获取所需的服务实例。
 * 使用模板方法确保编译时类型检查。
 */
class IServiceProvider
{
public:
    virtual ~IServiceProvider() = default;

    /**
     * @brief 获取指定类型的服务实例
     * @tparam T 服务接口类型，必须继承自IService
     * @return T* 服务实例指针，如果服务未注册或类型不匹配则返回nullptr
     * 
     * 使用示例:
     * ILogService* logger = provider->getService<ILogService>();
     * if (logger) {
     *     logger->info("Service obtained successfully");
     * }
     */
    template<typename T>
    T* getService()
    {
        static_assert(std::is_base_of_v<IService, T>, "T must inherit from IService");
        return static_cast<T*>(getServiceImpl(typeid(T)));
    }

protected:
    /**
     * @brief 内部实现方法，由具体的ServiceProvider实现
     * @param type 服务类型信息
     * @return IService* 服务实例指针
     */
    virtual IService* getServiceImpl(const std::type_info& type) = 0;
};
