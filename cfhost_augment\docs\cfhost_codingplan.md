# 宿主-插件框架编码计划 (Coding Plan)

本文档遵循渐进式小步迭代原则，为 `cfhost` 项目制定详细的开发步骤。每一步都旨在完成一个独立、可验证的功能点，确保项目在开发过程中的任何阶段都处于可运行状态。

## 核心流程实现图

```mermaid
sequenceDiagram
    participant main as main()
    participant HostApp as host::HostApplication
    participant PluginMgr as host::PluginManager
    participant ServiceMgr as host::ServiceManager
    participant IPlugin as interfaces::IPlugin

    main->>HostApp: 创建实例并传入命令行参数
    HostApp->>HostApp: 解析 --plugin-id
    HostApp->>PluginMgr: loadPlugin(pluginId)
    PluginMgr->>PluginMgr: 读取 plugin/config/pluginId.json
    PluginMgr->>ServiceMgr: createServiceProviderForPlugin(config)
    ServiceMgr->>ServiceMgr: 注册基础服务和依赖的可选服务
    ServiceMgr-->>PluginMgr: 返回 IServiceProvider*
    PluginMgr->>PluginMgr: QPluginLoader 加载插件 DLL
    PluginMgr-->>IPlugin: 创建插件实例
    PluginMgr->>IPlugin: setServiceProvider(provider)
    PluginMgr->>IPlugin: initialize()
    alt 初始化成功
        PluginMgr->>IPlugin: getWidget()
        IPlugin-->>PluginMgr: 返回 QWidget*
        PluginMgr-->>HostApp: 返回 IPlugin*
        HostApp->>HostApp: 将插件 Widget 设置为中心控件并显示窗口
    else 初始化失败
        PluginMgr-->>HostApp: 返回 nullptr
        HostApp->>HostApp: 记录错误日志并退出
    end
```

---

## 渐进式开发步骤

### 步骤 1: 项目框架搭建与核心接口定义

**目标:** 创建项目骨架和所有核心抽象接口，为后续模块实现提供契约。

1.  **创建目录结构:**
    *   在 `f:\Project\cfpixelengine\cfhost` 下创建 `host`, `interfaces`, `services`, `plugin/config` 目录。
2.  **创建Visual Studio项目:**
    *   在 Visual Studio 2022 中，使用 `Qt Widgets Application` 模板创建一个新的解决方案和项目，命名为 `cfhost`。
    *   确保项目的输出类型设置为“应用程序 (.exe)”。
    *   在项目属性中，配置好包含目录（`Additional Include Directories`）以指向 `interfaces`, `services`, `host` 等目录。
3.  **定义 `IService` 和 `IServiceProvider` 接口:**
    *   在 `interfaces/IService.h` 中定义 `IService` 空接口。
    *   在 `interfaces/IServiceProvider.h` 中定义 `IServiceProvider` 接口，包含模板方法 `virtual T* getService<T>() = 0;`。
4.  **定义 `IPlugin` 接口:**
    *   在 `interfaces/IPlugin.h` 中定义 `IPlugin` 接口。
    *   包含 `setServiceProvider`, `initialize`, `getWidget`, `shutdown` 等纯虚函数。
    *   为 `setServiceProvider` 提供一个默认实现，用于存储 `IServiceProvider` 指针。

**验证:** 项目可以成功编译通过，虽然此时它不执行任何操作。

### 步骤 2: 实现基础服务 - LogService

**目标:** 实现一个具体的基础服务，用于后续的日志记录。

1.  **创建 `LogService`:**
    *   在 `services/` 目录下创建 `LogService.h` 和 `LogService.cpp`。
    *   `LogService` 类继承自 `IService`。
    *   提供简单的日志记录方法，例如 `void info(const QString& message);`，当前可使用 `qDebug()` 实现。

**验证:** `LogService` 能够被实例化，并且项目可以成功编译。

### 步骤 3: 实现服务管理器 `ServiceManager`

**目标:** 创建一个能够注册和实例化服务的管理器。

1.  **实现 `ServiceManager`:**
    *   在 `host/` 目录下创建 `ServiceManager.h` 和 `ServiceManager.cpp`。
    *   实现 `registerService` 方法，用于注册服务（可以使用 `std::function` 作为服务工厂）。
    *   实现 `createServiceProviderForPlugin` 方法。此阶段，该方法仅创建一个包含已注册的 `LogService` 的 `ServiceProvider` 实例。

**验证:** 编写一个临时的 `main` 函数，在其中实例化 `ServiceManager`，注册 `LogService`，并成功创建一个包含 `LogService` 实例的 `ServiceProvider`。

### 步骤 4: 实现插件管理器 `PluginManager` (配置加载)

**目标:** 使 `PluginManager` 能够根据插件ID找到并解析JSON配置文件。

1.  **创建 `PluginManager`:**
    *   在 `host/` 目录下创建 `PluginManager.h` 和 `PluginManager.cpp`。
    *   在构造函数中接收一个 `ServiceManager` 的引用。
2.  **实现配置加载:**
    *   实现 `loadPlugin` 方法的初期版本。它将：
        *   接收 `pluginId` 作为参数。
        *   根据 `pluginId` 在 `plugin/config/` 目录下查找对应的 `.json` 文件。
        *   使用 `QJsonDocument` 解析配置文件，提取 `launcherDll` 和 `dependService` 列表。
        *   调用 `ServiceManager` 的 `createServiceProviderForPlugin` 方法。
        *   **注意:** 此阶段不加载DLL，仅打印解析到的信息和日志。
3.  **创建示例配置:**
    *   在 `plugin/config/` 目录下创建 `idphoto.json` 文件，内容参照LLD。

**验证:** 在 `main` 函数中调用 `pluginManager->loadPlugin("idphoto")`，观察控制台输出，确认配置文件被正确解析。

### 步骤 5: 实现宿主应用 `HostApplication`

**目标:** 创建主应用框架，负责解析命令行、集成并驱动各个管理器。

1.  **创建 `HostApplication`:**
    *   在 `host/` 目录下创建 `HostApplication.h` 和 `HostApplication.cpp`，继承自 `QObject`。
    *   在构造函数中初始化 `ServiceManager` 和 `PluginManager`。
2.  **实现命令行解析:**
    *   在 `main.cpp` 中，将 `QCoreApplication::arguments()` 传递给 `HostApplication`。
    *   `HostApplication` 解析 `--plugin-id` 参数。
3.  **集成流程:**
    *   实现 `run()` 方法，该方法调用 `PluginManager::loadPlugin`。
    *   如果加载成功（在此阶段意味着配置解析成功），则记录成功日志；否则记录失败日志。

**验证:** 从命令行运行 `cfhost.exe --plugin-id idphoto`，程序应能正确解析ID，并打印插件配置加载成功的日志。运行不带参数的程序应提示错误。

### 步骤 6: 实现插件加载与UI集成

**目标:** 完成插件DLL的动态加载，并将插件UI嵌入宿主窗口。

1.  **创建宿主UI:**
    *   修改 `HostApplication`，使其创建一个 `QMainWindow` 作为应用外壳。
2.  **完善 `PluginManager::loadPlugin`:**
    *   使用 `QPluginLoader` 加载配置文件中 `launcherDll` 指定的DLL。
    *   `qobject_cast` 转换为 `IPlugin*` 接口。
    *   调用 `plugin->setServiceProvider()` 注入服务。
    *   调用 `plugin->initialize()`。
3.  **完善 `HostApplication`:**
    *   如果 `loadPlugin` 返回有效的 `IPlugin` 实例：
        *   调用 `plugin->getWidget()` 获取插件UI。
        *   将获取到的 `QWidget*` 设置为 `QMainWindow` 的中央控件。
        *   根据插件配置中的 `appName` 设置窗口标题。
        *   显示主窗口。
4.  **创建示例插件 `idphoto`:**
    *   在同一个解决方案下，添加一个新项目，使用 `Qt Class Library` 模板，命名为 `idphoto`，用于生成 `idphoto.dll`。
    *   此项目需要引用 `cfhost` 的 `interfaces`。
    *   实现一个 `IdPhotoPlugin` 类，继承 `QObject` 和 `IPlugin`。
    *   在 `getWidget()` 中返回一个简单的 `QLabel` 或 `QPushButton`。
    *   使用 `Q_INTERFACES` 和 `Q_PLUGIN_METADATA` 宏将其导出为Qt插件。

**验证:**
1.  将编译好的 `idphoto.dll` 放置到 `cfhost.exe` 同级目录。
2.  运行 `cfhost.exe --plugin-id idphoto`。
3.  应能看到一个标题为“证件照”的窗口弹出，内容为 `idphoto` 插件提供的简单控件。

### 步骤 7: 实现完整的生命周期管理

**目标:** 确保插件的 `shutdown` 方法在应用退出时被正确调用。

1.  **连接退出信号:**
    *   在 `HostApplication` 中，连接 `QCoreApplication::aboutToQuit` 信号到一个新的槽函数。
2.  **实现清理逻辑:**
    *   在该槽函数中，调用已加载插件实例的 `shutdown()` 方法。
    *   在 `IdPhotoPlugin` 的 `shutdown()` 方法中添加一条日志输出，以验证其被调用。

**验证:** 关闭由宿主启动的插件窗口，观察控制台，应能看到 `IdPhotoPlugin::shutdown()` 中打印的日志信息。

---
