#pragma once

#include <QObject>
#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <memory>
#include "IPlugin.h"
#include "ILogService.h"

/**
 * @brief 证件照插件实现
 * 
 * 这是一个示例插件，展示如何实现IPlugin接口
 * 并使用宿主提供的服务。
 */
class IdPhotoPlugin : public QObject, public IPlugin
{
    Q_OBJECT
    Q_INTERFACES(IPlugin)
    Q_PLUGIN_METADATA(IID "com.cfpixelengine.IPlugin/1.0" FILE "idphoto.json")

public:
    IdPhotoPlugin(QObject* parent = nullptr);
    virtual ~IdPhotoPlugin();

    // IPlugin接口实现
    bool initialize() override;
    QWidget* getWidget() override;
    void shutdown() override;

private slots:
    /**
     * @brief 处理按钮点击事件
     */
    void onButtonClicked();

private:
    /**
     * @brief 创建插件UI
     */
    void createUI();

    /**
     * @brief 插件主界面
     */
    std::unique_ptr<QWidget> m_mainWidget;

    /**
     * @brief 状态标签
     */
    QLabel* m_statusLabel;

    /**
     * @brief 测试按钮
     */
    QPushButton* m_testButton;

    /**
     * @brief 日志服务
     */
    ILogService* m_logService;
};
