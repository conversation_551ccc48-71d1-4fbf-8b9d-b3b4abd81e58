#include <QApplication>
#include <QDebug>
#include "IService.h"
#include "IServiceProvider.h"
#include "IPlugin.h"
#include "ILogService.h"
#include "LogService.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);

    qDebug() << "cfhost framework initialized successfully";
    qDebug() << "Core interfaces defined:";
    qDebug() << "  - IService";
    qDebug() << "  - IServiceProvider";
    qDebug() << "  - IPlugin";
    qDebug() << "  - ILogService";

    // 验证LogService可以被实例化
    LogService logService;
    logService.info("LogService created successfully");
    logService.debug("Testing debug message");
    logService.warning("Testing warning message");
    logService.error("Testing error message");

    qDebug() << "Step 2 verification completed: LogService implementation";

    // 暂时不启动事件循环，只验证编译
    return 0;
}
