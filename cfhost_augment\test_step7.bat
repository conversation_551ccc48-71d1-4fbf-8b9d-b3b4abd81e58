@echo off
echo Testing Step 7: Complete Lifecycle Management
echo.

echo This test will verify that plugin shutdown is called correctly.
echo.

echo Building the project...
msbuild Host.sln /p:Configuration=Debug /p:Platform=Win32 /nologo /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful! Starting lifecycle test...
echo.

echo Instructions for manual testing:
echo 1. The application will start and show the idphoto plugin window
echo 2. Try closing the window using different methods:
echo    - Click the X button on the window
echo    - Use File -> Exit menu
echo    - Press Alt+F4
echo 3. Watch the console output for shutdown messages
echo.

echo Expected output should include:
echo - "Application is about to quit - performing cleanup..."
echo - "=== IdPhotoPlugin::shutdown() called ==="
echo - "IdPhotoPlugin: Starting shutdown process..."
echo - "=== IdPhotoPlugin shutdown completed ==="
echo.

echo Starting application...
echo.

cd Debug
Host.exe --plugin-id idphoto

echo.
echo Test completed! Check the console output above for shutdown messages.
pause
