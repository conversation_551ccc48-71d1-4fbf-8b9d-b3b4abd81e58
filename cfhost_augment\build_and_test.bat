@echo off
echo Building cfhost framework and idphoto plugin...
echo.

echo Step 1: Building Host application...
msbuild Host.sln /p:Configuration=Debug /p:Platform=Win32 /t:Host

if %ERRORLEVEL% neq 0 (
    echo Failed to build Host application
    pause
    exit /b 1
)

echo.
echo Step 2: Building idphoto plugin...
msbuild Host.sln /p:Configuration=Debug /p:Platform=Win32 /t:idphoto

if %ERRORLEVEL% neq 0 (
    echo Failed to build idphoto plugin
    pause
    exit /b 1
)

echo.
echo Step 3: Copying plugin DLL to output directory...
copy "Debug\idphoto.dll" "Debug\" >nul 2>&1

echo.
echo Step 4: Testing the framework...
echo Running: Debug\Host.exe --plugin-id idphoto
echo.

cd Debug
Host.exe --plugin-id idphoto

echo.
echo Build and test completed!
pause
