#pragma once

#include <functional>
#include <unordered_map>
#include <string>
#include <memory>
#include <QJsonObject>
#include "IService.h"
#include "IServiceProvider.h"

/**
 * @brief 服务管理器
 * 
 * 负责注册服务工厂，并根据插件配置创建专属的服务提供者。
 * 支持基础服务和可选服务的管理。
 */
class ServiceManager
{
public:
    /**
     * @brief 服务工厂函数类型
     * 
     * 返回服务实例的智能指针
     */
    using ServiceFactory = std::function<std::shared_ptr<IService>()>;

    ServiceManager();
    virtual ~ServiceManager() = default;

    /**
     * @brief 注册服务工厂
     * @param serviceName 服务名称
     * @param factory 服务工厂函数
     */
    void registerServiceFactory(const std::string& serviceName, ServiceFactory factory);

    /**
     * @brief 为插件创建专属的服务提供者
     * @param pluginConfig 插件配置信息
     * @return IServiceProvider* 服务提供者实例，失败时返回nullptr
     */
    IServiceProvider* createServiceProviderForPlugin(const QJsonObject& pluginConfig);

private:
    /**
     * @brief 初始化基础服务工厂
     * 
     * 注册所有基础服务的工厂函数
     */
    void initializeBasicServices();

    /**
     * @brief 服务工厂映射表
     * 
     * 键：服务名称
     * 值：服务工厂函数
     */
    std::unordered_map<std::string, ServiceFactory> m_serviceFactories;
};
