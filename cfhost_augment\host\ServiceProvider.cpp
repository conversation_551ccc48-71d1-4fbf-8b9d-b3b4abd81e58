#include "ServiceProvider.h"
#include <typeindex>

ServiceProvider::ServiceProvider()
{
    // 构造函数，初始化服务映射表
}

ServiceProvider::~ServiceProvider()
{
    // 析构函数，智能指针会自动管理服务实例的生命周期
    m_services.clear();
}

IService* ServiceProvider::getServiceImpl(const std::type_info& type)
{
    std::type_index typeIndex(type);
    auto it = m_services.find(typeIndex);
    
    if (it != m_services.end()) {
        return it->second.get();
    }
    
    // 服务未找到，返回nullptr
    return nullptr;
}
