#pragma once

#include <QString>
#include <QJsonObject>
#include <QPluginLoader>
#include <memory>
#include <unordered_map>
#include "IPlugin.h"
#include "ServiceManager.h"

/**
 * @brief 插件管理器
 * 
 * 负责插件的查找、配置解析、加载和生命周期管理。
 * 与ServiceManager协作为插件提供所需的服务。
 */
class PluginManager
{
public:
    /**
     * @brief 构造函数
     * @param serviceManager 服务管理器引用
     */
    explicit PluginManager(ServiceManager& serviceManager);
    virtual ~PluginManager() = default;

    /**
     * @brief 加载插件
     * @param pluginId 插件ID
     * @return IPlugin* 插件实例指针，失败时返回nullptr
     * 
     * 完整的插件加载流程：
     * 1. 根据pluginId查找配置文件
     * 2. 解析配置文件
     * 3. 创建服务提供者
     * 4. 加载插件DLL（当前阶段暂不实现）
     * 5. 初始化插件
     */
    IPlugin* loadPlugin(const QString& pluginId);

    /**
     * @brief 卸载插件
     * @param plugin 插件实例指针
     */
    void unloadPlugin(IPlugin* plugin);

    /**
     * @brief 获取最后加载的插件配置
     * @return QJsonObject 插件配置对象
     */
    const QJsonObject& getLastLoadedPluginConfig() const;

private:
    /**
     * @brief 查找插件配置文件
     * @param pluginId 插件ID
     * @return QString 配置文件路径，未找到时返回空字符串
     */
    QString findPluginConfigFile(const QString& pluginId) const;

    /**
     * @brief 解析插件配置文件
     * @param configFilePath 配置文件路径
     * @param outConfig 输出的配置对象
     * @return bool 解析成功返回true，失败返回false
     */
    bool parsePluginConfig(const QString& configFilePath, QJsonObject& outConfig) const;

    /**
     * @brief 验证插件配置的有效性
     * @param config 配置对象
     * @return bool 配置有效返回true，无效返回false
     */
    bool validatePluginConfig(const QJsonObject& config) const;

    /**
     * @brief 加载插件DLL
     * @param dllPath DLL文件路径
     * @param config 插件配置
     * @return IPlugin* 插件实例，失败时返回nullptr
     */
    IPlugin* loadPluginDll(const QString& dllPath, const QJsonObject& config);

    /**
     * @brief 服务管理器引用
     */
    ServiceManager& m_serviceManager;

    /**
     * @brief 插件配置文件目录
     */
    static const QString PLUGIN_CONFIG_DIR;

    /**
     * @brief 已加载的插件加载器映射
     *
     * 键：插件实例指针
     * 值：对应的QPluginLoader
     */
    std::unordered_map<IPlugin*, std::unique_ptr<QPluginLoader>> m_loadedPlugins;

    /**
     * @brief 最后加载的插件配置
     */
    QJsonObject m_lastLoadedConfig;
};
