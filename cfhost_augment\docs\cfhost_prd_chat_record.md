# 需求讨论记录

本文档记录了“像素引擎宿主-插件框架”PRD形成过程中的核心问答与决策过程。

---

### **讨论 0：初始需求**

**【用户】原始需求：**
```
目标
你的目标是产出一份需求PRD文档

背景
背景是我们有很多独立的大的图片编辑能力，比如批量处理，转换格式等。我们在后续开发过程中，每次新建项目都需要重复接入非常多的基础模块，比如会员、埋点、网络、下载等。
为此我们设计了程序运行的框架：宿主程序 + 插件dll的形式。宿主程序本身没有界面展示，它负责提供能力或服务，插件提供内容展示和处理与用户的交互逻辑。

详细说明
1.插件可以非常简单地获取宿主提供的能力或服务，插件不知道能力的实现细节，它在宿主处被隐藏和实现

2.宿主和插件是一对多的关系，所以这里会有一个映射关系，我们的设计是通过解析命令行传入一个插件id，在配置文件中找到对应的映射关系。
同时配置文件会包含插件的一些配置说明，具体如下所示：
{
    "baseInfo": {
        "appId": "idphoto",
        "appName": "证件照",
        "buildVersion": 1
    },
    "runEnvInfo": {
        "appClassify": {
            "appType": "plugin",
            "dependService": [
                "base"
            ],
            "launcherDll": "idphoto.dll",
            "launcherType": "native"
        }
    }
}

流程
1.宿主程序启动后，解析命令行启动参数，获取插件id以及插件配置
2.根据插件的配置的dependService信息，注册服务，为提供给插件做准备
3.找到配置中需要启动的dll，使用Qt Plugin机制进行加载
4.随后开始执行插件的服务注入，以及初始化，最后获取插件提供的QWidget作为窗口显示的内容
```

---

### **讨论 1：服务定义与管理**

**【用户】初步需求：**
*   **目标：** 设计一个“宿主-插件”框架，宿主提供服务，插件实现UI和业务逻辑。
*   **流程：** 宿主通过命令行参数加载指定插件，根据插件配置中的 `dependService` 注册服务，然后加载插件DLL并注入服务。

**【Cascade】提问：**
1.  **服务依赖失败：** 如果宿主无法提供插件所需的服务，应如何处理？是报错还是降级运行？
2.  **服务粒度：** `base` 服务具体包含什么？是提供一个大的基础包，还是拆分成更细粒度的服务？
3.  **服务版本：** 是否需要支持服务版本控制？

**【用户】决策：**
1.  **快速失败：** 遵循快速失败原则。服务注入阶段检查，若服务缺失，则直接拒绝加载插件并记录错误日志。
2.  **服务分类：** `base` 字段多余，应废除。框架应默认提供所有“基础服务”（如会员、日志），`dependService` 字段仅用于声明“可选服务”。
3.  **版本管理：** 暂时不做。为保持框架简洁，当前阶段不引入版本控制，所有插件和宿主共享一套接口。

---

### **讨论 2：插件生命周期与错误处理**

**【Cascade】提问：**
1.  **配置文件健壮性：** 如果插件的JSON配置文件本身有问题（如丢失、格式错误），宿主应如何响应？
2.  **插件生命周期：** 是否需要为插件定义一套明确的生命周期接口，如 `initialize`, `getWidget`, `shutdown`？

**【用户】决策：**
1.  **静默失败：** 若配置文件有问题，宿主程序不弹出任何UI，仅在日志中记录错误信息，然后终止加载。
2.  **生命周期确认：** 同意需要一个完整的生命周期。除了初始化 (`initialize`) 和获取UI (`getWidget`)，确实需要一个退出回调 (`shutdown`) 用于清理工作。

---

### **讨论 3：运行时交互**

**【Cascade】提问：**
1.  **宿主到插件通信：** 宿主如何将全局事件（如用户登出）通知给插件？
2.  **窗口控制权：** 主窗口的标题、图标等属性由谁决定？
3.  **共享UI：** 宿主是否提供共享的UI区域（如菜单栏），插件能否控制它们？

**【用户】决策：**
1.  **暂不考虑：** 宿主没有业务逻辑，因此暂时不考虑宿主向插件主动通信的场景。
2.  **插件配置控制：** 窗口外观由插件的配置文件（`baseInfo`部分）决定。
3.  **宿主统一提供：** 宿主提供统一的、共享的顶部菜单栏，插件不能控制。未来可能将菜单栏封装成一个可选服务。

---

### **讨论 4：插件生态与边界**

**【Cascade】提问：**
1.  **插件间通信：** 是否允许一个插件调用另一个插件的功能？
2.  **多实例：** 是否支持同时加载一个插件的多个实例？
3.  **数据传递：** 跨插件的数据流转（如转换图片后再上传）如何处理？

**【用户】决策：**
1.  **禁止通信：** 插件间不能直接通信。任何可复用的功能都应沉淀为宿主提供的可选服务。
2.  **（初步）暂不考虑：** 初期设计不考虑多实例的复杂性。
3.  **插件内部负责：** 数据的暂存、管理和流转逻辑完全由插件自身负责。

---

### **讨论 5：多实例架构澄清**

**【Cascade】提问：**
*   基于之前的讨论，准备开始撰写PRD。

**【用户】决策（重要澄清）：**
*   关于多实例，设计是通过启动 **多个独立的宿主进程** 来实现的，而非在一个进程内加载多个实例。启动时会通过命令行传递 `instance_id` 和 `multi` 标志来区分。

**【Cascade】最终确认：**
*   重新理解并确认了多实例架构：**一个宿主进程 = 一个插件实例**。多实例是通过多进程实现的。这需要在PRD中明确命令行规范，并指出服务设计需要感知 `instance_id`。

---

### **最终决策**

**【用户】决策：**
*   可以，产出PRD文档吧。
