#include "PluginManager.h"
#include <QDir>
#include <QFile>
#include <QJsonDocument>
#include <QJsonParseError>
#include <QJsonArray>
#include <QDebug>
#include <QCoreApplication>

// 静态成员定义
const QString PluginManager::PLUGIN_CONFIG_DIR = "plugin/config";

PluginManager::PluginManager(ServiceManager& serviceManager)
    : m_serviceManager(serviceManager)
{
    qDebug() << "PluginManager initialized";
}

IPlugin* PluginManager::loadPlugin(const QString& pluginId)
{
    qDebug() << QString("Loading plugin: %1").arg(pluginId);
    
    // 1. 查找配置文件
    QString configFilePath = findPluginConfigFile(pluginId);
    if (configFilePath.isEmpty()) {
        qCritical() << QString("Plugin config file not found for: %1").arg(pluginId);
        return nullptr;
    }
    
    qDebug() << QString("Found config file: %1").arg(configFilePath);
    
    // 2. 解析配置文件
    QJsonObject config;
    if (!parsePluginConfig(configFilePath, config)) {
        qCritical() << QString("Failed to parse plugin config: %1").arg(configFilePath);
        return nullptr;
    }
    
    // 3. 验证配置有效性
    if (!validatePluginConfig(config)) {
        qCritical() << QString("Invalid plugin config: %1").arg(configFilePath);
        return nullptr;
    }
    
    qDebug() << "Plugin config parsed and validated successfully";

    // 保存配置以供后续使用
    m_lastLoadedConfig = config;

    // 4. 创建服务提供者
    IServiceProvider* serviceProvider = m_serviceManager.createServiceProviderForPlugin(config);
    if (!serviceProvider) {
        qCritical() << QString("Failed to create service provider for plugin: %1").arg(pluginId);
        return nullptr;
    }
    
    qDebug() << "Service provider created successfully";
    
    // 5. 加载插件DLL
    QJsonObject runEnvInfo = config["runEnvInfo"].toObject();
    QJsonObject appClassify = runEnvInfo["appClassify"].toObject();
    QString launcherDll = appClassify["launcherDll"].toString();

    qDebug() << QString("Loading plugin DLL: %1").arg(launcherDll);

    IPlugin* plugin = loadPluginDll(launcherDll, config);
    if (!plugin) {
        qCritical() << QString("Failed to load plugin DLL: %1").arg(launcherDll);
        delete serviceProvider;
        return nullptr;
    }

    // 6. 注入服务提供者
    plugin->setServiceProvider(serviceProvider);

    // 7. 初始化插件
    if (!plugin->initialize()) {
        qCritical() << QString("Plugin initialization failed: %1").arg(pluginId);
        unloadPlugin(plugin);
        delete serviceProvider;
        return nullptr;
    }

    qDebug() << QString("Plugin loaded and initialized successfully: %1").arg(pluginId);
    return plugin;
}

void PluginManager::unloadPlugin(IPlugin* plugin)
{
    if (!plugin) {
        return;
    }

    // 调用插件的shutdown方法
    plugin->shutdown();

    // 查找并卸载对应的插件加载器
    auto it = m_loadedPlugins.find(plugin);
    if (it != m_loadedPlugins.end()) {
        QPluginLoader* loader = it->second.get();
        if (loader->isLoaded()) {
            loader->unload();
            qDebug() << QString("Plugin DLL unloaded: %1").arg(loader->fileName());
        }
        m_loadedPlugins.erase(it);
    }

    qDebug() << "Plugin unloaded successfully";
}

QString PluginManager::findPluginConfigFile(const QString& pluginId) const
{
    // 构建配置文件路径
    QString configFileName = QString("%1.json").arg(pluginId);
    QString configFilePath = QString("%1/%2").arg(PLUGIN_CONFIG_DIR, configFileName);
    
    // 检查文件是否存在
    QFile configFile(configFilePath);
    if (configFile.exists()) {
        return configFilePath;
    }
    
    // 如果相对路径不存在，尝试绝对路径
    QString absolutePath = QCoreApplication::applicationDirPath() + "/" + configFilePath;
    QFile absoluteFile(absolutePath);
    if (absoluteFile.exists()) {
        return absolutePath;
    }
    
    return QString(); // 未找到
}

bool PluginManager::parsePluginConfig(const QString& configFilePath, QJsonObject& outConfig) const
{
    QFile configFile(configFilePath);
    if (!configFile.open(QIODevice::ReadOnly)) {
        qCritical() << QString("Cannot open config file: %1").arg(configFilePath);
        return false;
    }
    
    QByteArray configData = configFile.readAll();
    configFile.close();
    
    QJsonParseError parseError;
    QJsonDocument configDoc = QJsonDocument::fromJson(configData, &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        qCritical() << QString("JSON parse error: %1").arg(parseError.errorString());
        return false;
    }
    
    if (!configDoc.isObject()) {
        qCritical() << "Config file root is not a JSON object";
        return false;
    }
    
    outConfig = configDoc.object();
    return true;
}

bool PluginManager::validatePluginConfig(const QJsonObject& config) const
{
    // 验证必需的字段
    if (!config.contains("baseInfo") || !config["baseInfo"].isObject()) {
        qCritical() << "Missing or invalid 'baseInfo' field";
        return false;
    }
    
    if (!config.contains("runEnvInfo") || !config["runEnvInfo"].isObject()) {
        qCritical() << "Missing or invalid 'runEnvInfo' field";
        return false;
    }
    
    QJsonObject baseInfo = config["baseInfo"].toObject();
    if (!baseInfo.contains("appId") || !baseInfo["appId"].isString()) {
        qCritical() << "Missing or invalid 'baseInfo.appId' field";
        return false;
    }
    
    if (!baseInfo.contains("appName") || !baseInfo["appName"].isString()) {
        qCritical() << "Missing or invalid 'baseInfo.appName' field";
        return false;
    }
    
    QJsonObject runEnvInfo = config["runEnvInfo"].toObject();
    if (!runEnvInfo.contains("appClassify") || !runEnvInfo["appClassify"].isObject()) {
        qCritical() << "Missing or invalid 'runEnvInfo.appClassify' field";
        return false;
    }
    
    QJsonObject appClassify = runEnvInfo["appClassify"].toObject();
    if (!appClassify.contains("launcherDll") || !appClassify["launcherDll"].isString()) {
        qCritical() << "Missing or invalid 'runEnvInfo.appClassify.launcherDll' field";
        return false;
    }
    
    // dependService字段是可选的，但如果存在必须是数组
    if (appClassify.contains("dependService") && !appClassify["dependService"].isArray()) {
        qCritical() << "Invalid 'runEnvInfo.appClassify.dependService' field (must be array)";
        return false;
    }
    
    qDebug() << "Plugin config validation passed";
    return true;
}

IPlugin* PluginManager::loadPluginDll(const QString& dllPath, const QJsonObject& config)
{
    // 构建完整的DLL路径
    QString fullDllPath = dllPath;

    // 如果是相对路径，尝试在应用程序目录中查找
    if (!QFile::exists(fullDllPath)) {
        fullDllPath = QCoreApplication::applicationDirPath() + "/" + dllPath;
    }

    if (!QFile::exists(fullDllPath)) {
        qCritical() << QString("Plugin DLL not found: %1").arg(dllPath);
        return nullptr;
    }

    qDebug() << QString("Loading DLL from: %1").arg(fullDllPath);

    // 创建插件加载器
    auto loader = std::make_unique<QPluginLoader>(fullDllPath);

    // 加载插件
    QObject* pluginObject = loader->instance();
    if (!pluginObject) {
        qCritical() << QString("Failed to load plugin: %1").arg(loader->errorString());
        return nullptr;
    }

    // 转换为IPlugin接口
    IPlugin* plugin = qobject_cast<IPlugin*>(pluginObject);
    if (!plugin) {
        qCritical() << QString("Plugin does not implement IPlugin interface: %1").arg(dllPath);
        loader->unload();
        return nullptr;
    }

    // 保存加载器以便后续卸载
    m_loadedPlugins[plugin] = std::move(loader);

    qDebug() << QString("Plugin DLL loaded successfully: %1").arg(dllPath);
    return plugin;
}

const QJsonObject& PluginManager::getLastLoadedPluginConfig() const
{
    return m_lastLoadedConfig;
}
