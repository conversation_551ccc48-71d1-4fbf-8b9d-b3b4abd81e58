#pragma once

#include <QString>
#include <QDateTime>
#include "ILogService.h"

/**
 * @brief 日志服务实现
 * 
 * 基础的日志服务实现，当前使用Qt的调试输出系统。
 * 未来可以扩展为文件日志、网络日志等。
 */
class LogService : public ILogService
{
public:
    LogService();
    virtual ~LogService() = default;

    // ILogService接口实现
    void info(const QString& message) override;
    void warning(const QString& message) override;
    void error(const QString& message) override;
    void debug(const QString& message) override;

private:
    /**
     * @brief 格式化日志消息
     * @param level 日志级别
     * @param message 原始消息
     * @return 格式化后的消息
     */
    QString formatMessage(const QString& level, const QString& message) const;
};
