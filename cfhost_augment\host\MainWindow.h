#pragma once

#include <QMainWindow>
#include <QCloseEvent>

/**
 * @brief 自定义主窗口类
 * 
 * 提供窗口关闭事件的处理，确保在用户关闭窗口时
 * 能够正确触发应用程序的清理流程。
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget* parent = nullptr);
    virtual ~MainWindow() = default;

signals:
    /**
     * @brief 窗口即将关闭信号
     * 
     * 在窗口关闭前发出，允许应用程序执行清理操作
     */
    void aboutToClose();

protected:
    /**
     * @brief 重写关闭事件处理
     * @param event 关闭事件
     */
    void closeEvent(QCloseEvent* event) override;
};
