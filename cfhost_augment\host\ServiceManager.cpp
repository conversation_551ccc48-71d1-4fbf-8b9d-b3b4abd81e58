#include "ServiceManager.h"
#include "ServiceProvider.h"
#include "LogService.h"
#include "ILogService.h"
#include <QJsonArray>
#include <QDebug>

ServiceManager::ServiceManager()
{
    initializeBasicServices();
}

void ServiceManager::registerServiceFactory(const std::string& serviceName, ServiceFactory factory)
{
    m_serviceFactories[serviceName] = factory;
    qDebug() << QString("Service factory registered: %1").arg(QString::fromStdString(serviceName));
}

IServiceProvider* ServiceManager::createServiceProviderForPlugin(const QJsonObject& pluginConfig)
{
    auto provider = new ServiceProvider();
    
    // 1. 注册所有基础服务
    // LogService是基础服务，所有插件都可以使用
    auto logServiceFactory = m_serviceFactories.find("logService");
    if (logServiceFactory != m_serviceFactories.end()) {
        auto logService = logServiceFactory->second();
        provider->registerService<ILogService>(std::static_pointer_cast<ILogService>(logService));
        qDebug() << "Basic service registered: logService";
    }
    
    // 2. 根据插件配置注册可选服务
    QJsonObject runEnvInfo = pluginConfig["runEnvInfo"].toObject();
    QJsonObject appClassify = runEnvInfo["appClassify"].toObject();
    QJsonArray dependServices = appClassify["dependService"].toArray();
    
    for (const auto& serviceValue : dependServices) {
        std::string serviceName = serviceValue.toString().toStdString();
        
        auto factoryIt = m_serviceFactories.find(serviceName);
        if (factoryIt != m_serviceFactories.end()) {
            auto service = factoryIt->second();
            // 注意：这里需要根据具体的服务类型进行转换
            // 当前示例中，我们假设所有服务都可以作为IService注册
            // 实际项目中可能需要更复杂的类型处理
            qDebug() << QString("Optional service registered: %1").arg(QString::fromStdString(serviceName));
        } else {
            qCritical() << QString("Required service not found: %1").arg(QString::fromStdString(serviceName));
            delete provider;
            return nullptr;
        }
    }
    
    return provider;
}

void ServiceManager::initializeBasicServices()
{
    // 注册LogService工厂
    registerServiceFactory("logService", []() -> std::shared_ptr<IService> {
        return std::make_shared<LogService>();
    });
    
    qDebug() << "Basic services initialized";
}
