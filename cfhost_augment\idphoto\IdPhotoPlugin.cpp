#include "IdPhotoPlugin.h"
#include <QDebug>
#include <QMessageBox>

IdPhotoPlugin::IdPhotoPlugin(QObject* parent)
    : QObject(parent)
    , m_status<PERSON>abel(nullptr)
    , m_testButton(nullptr)
    , m_logService(nullptr)
{
    qDebug() << "IdPhotoPlugin created";
}

IdPhotoPlugin::~IdPhotoPlugin()
{
    qDebug() << "IdPhotoPlugin destroyed";
}

bool IdPhotoPlugin::initialize()
{
    qDebug() << "IdPhotoPlugin initializing...";
    
    // 获取日志服务
    if (m_serviceProvider) {
        m_logService = m_serviceProvider->getService<ILogService>();
        if (m_logService) {
            m_logService->info("IdPhotoPlugin: LogService obtained successfully");
        } else {
            qCritical() << "IdPhotoPlugin: Failed to get LogService";
            return false;
        }
    } else {
        qCritical() << "IdPhotoPlugin: ServiceProvider not set";
        return false;
    }
    
    // 创建UI
    createUI();
    
    if (m_logService) {
        m_logService->info("IdPhotoPlugin initialized successfully");
    }
    
    qDebug() << "IdPhotoPlugin initialization completed";
    return true;
}

QWidget* IdPhotoPlugin::getWidget()
{
    if (m_logService) {
        m_logService->debug("IdPhotoPlugin: getWidget() called");
    }
    
    return m_mainWidget.get();
}

void IdPhotoPlugin::shutdown()
{
    qDebug() << "=== IdPhotoPlugin::shutdown() called ===";

    if (m_logService) {
        m_logService->info("IdPhotoPlugin: Starting shutdown process...");
        m_logService->warning("IdPhotoPlugin: This message verifies that shutdown() was called correctly");
    }

    // 保存用户数据（示例）
    qDebug() << "IdPhotoPlugin: Saving user data...";
    if (m_logService) {
        m_logService->info("IdPhotoPlugin: User data saved successfully");
    }

    // 释放资源
    qDebug() << "IdPhotoPlugin: Releasing UI resources...";
    m_mainWidget.reset();
    m_statusLabel = nullptr;
    m_testButton = nullptr;

    // 清理服务引用
    if (m_logService) {
        m_logService->info("IdPhotoPlugin: Cleanup completed successfully");
        m_logService = nullptr;
    }

    qDebug() << "=== IdPhotoPlugin shutdown completed ===";
}

void IdPhotoPlugin::createUI()
{
    qDebug() << "Creating IdPhotoPlugin UI...";
    
    // 创建主控件
    m_mainWidget = std::make_unique<QWidget>();
    m_mainWidget->setMinimumSize(400, 300);
    
    // 创建布局
    QVBoxLayout* layout = new QVBoxLayout(m_mainWidget.get());
    
    // 创建标题标签
    QLabel* titleLabel = new QLabel("证件照处理插件", m_mainWidget.get());
    titleLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;");
    titleLabel->setAlignment(Qt::AlignCenter);
    layout->addWidget(titleLabel);
    
    // 创建状态标签
    m_statusLabel = new QLabel("插件已就绪", m_mainWidget.get());
    m_statusLabel->setStyleSheet("font-size: 14px; color: #27ae60; margin: 5px;");
    m_statusLabel->setAlignment(Qt::AlignCenter);
    layout->addWidget(m_statusLabel);
    
    // 创建测试按钮
    m_testButton = new QPushButton("测试功能", m_mainWidget.get());
    m_testButton->setStyleSheet("QPushButton { font-size: 14px; padding: 10px; background-color: #3498db; color: white; border: none; border-radius: 5px; } QPushButton:hover { background-color: #2980b9; }");
    connect(m_testButton, &QPushButton::clicked, this, &IdPhotoPlugin::onButtonClicked);
    layout->addWidget(m_testButton);
    
    // 添加弹性空间
    layout->addStretch();
    
    // 创建信息标签
    QLabel* infoLabel = new QLabel("这是一个示例插件，展示了宿主-插件框架的基本功能。", m_mainWidget.get());
    infoLabel->setStyleSheet("font-size: 12px; color: #7f8c8d; margin: 10px;");
    infoLabel->setAlignment(Qt::AlignCenter);
    infoLabel->setWordWrap(true);
    layout->addWidget(infoLabel);
    
    qDebug() << "IdPhotoPlugin UI created successfully";
}

void IdPhotoPlugin::onButtonClicked()
{
    if (m_logService) {
        m_logService->info("IdPhotoPlugin: Test button clicked");
    }
    
    // 更新状态
    if (m_statusLabel) {
        m_statusLabel->setText("测试功能已执行");
        m_statusLabel->setStyleSheet("font-size: 14px; color: #e74c3c; margin: 5px;");
    }
    
    // 显示消息框
    QMessageBox::information(m_mainWidget.get(), "测试", "证件照插件功能测试成功！\n\n这证明了：\n1. 插件成功加载\n2. UI正确集成\n3. 服务注入工作正常");
    
    qDebug() << "IdPhotoPlugin test function executed";
}

#include "IdPhotoPlugin.moc"
