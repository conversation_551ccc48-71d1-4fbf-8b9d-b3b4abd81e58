#pragma once

#include <QObject>
#include <QStringList>
#include <memory>
#include "ServiceManager.h"
#include "PluginManager.h"
#include "IPlugin.h"
#include "MainWindow.h"

/**
 * @brief 宿主应用程序类
 * 
 * 负责整个应用的生命周期管理，包括命令行解析、
 * 管理器初始化、插件加载等核心流程。
 */
class HostApplication : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param arguments 命令行参数列表
     * @param parent 父对象
     */
    explicit HostApplication(const QStringList& arguments, QObject* parent = nullptr);
    virtual ~HostApplication();

    /**
     * @brief 运行应用程序
     * @return int 应用程序退出码，0表示成功
     */
    int run();

private:
    /**
     * @brief 解析命令行参数
     * @return bool 解析成功返回true，失败返回false
     */
    bool parseCommandLine();

    /**
     * @brief 初始化管理器
     */
    void initializeManagers();

    /**
     * @brief 创建主窗口UI
     */
    void createMainWindow();

    /**
     * @brief 显示帮助信息
     */
    void showHelp() const;

    /**
     * @brief 显示错误信息并退出
     * @param errorMessage 错误消息
     */
    void showErrorAndExit(const QString& errorMessage) const;

    /**
     * @brief 清理当前插件
     */
    void cleanupPlugin();

private slots:
    /**
     * @brief 应用程序即将退出时的清理槽函数
     */
    void onAboutToQuit();

private:
    /**
     * @brief 命令行参数列表
     */
    QStringList m_arguments;

    /**
     * @brief 插件ID
     */
    QString m_pluginId;

    /**
     * @brief 实例ID（可选）
     */
    QString m_instanceId;

    /**
     * @brief 服务管理器
     */
    std::unique_ptr<ServiceManager> m_serviceManager;

    /**
     * @brief 插件管理器
     */
    std::unique_ptr<PluginManager> m_pluginManager;

    /**
     * @brief 当前加载的插件实例
     */
    IPlugin* m_currentPlugin;

    /**
     * @brief 主窗口
     */
    std::unique_ptr<MainWindow> m_mainWindow;
};
