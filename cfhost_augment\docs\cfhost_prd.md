# 像素引擎宿主-插件框架产品需求文档 (PRD)

| 版本 | 日期       | 作者   | 备注     |
| ---- | ---------- | ------ | -------- |
| 1.0  | 2025-07-11 | Cascade | 初始版本 |

## 1. 背景与目标

### 1.1 背景

当前，我们团队拥有众多独立的、功能强大的图片编辑与处理能力（如批量处理、格式转换、智能抠图等）。然而，在开发新的应用或功能时，每个项目都需要重复集成一系列基础模块，例如会员系统、日志记录、数据埋点、网络请求和文件下载等。这种重复性的工作不仅降低了开发效率，也增加了代码的维护成本和复杂性。

### 1.2 目标

为解决上述问题，我们旨在设计并构建一个通用的、可扩展的 **“宿主-插件”** 模式软件框架。该框架的核心目标是：

*   **能力复用：** 将通用的基础能力沉淀到宿主程序中，作为共享服务提供给所有业务插件，避免重复开发。
*   **关注分离：** 宿主程序负责提供稳定、可靠的基础服务和运行环境；插件则聚焦于具体的业务逻辑和用户界面实现。
*   **提升效率：** 插件开发者可以快速、简单地接入所需服务，专注于业务创新，从而显著提升开发效率和迭代速度。
*   **隔离与稳定：** 插件之间相互隔离，单个插件的故障不影响宿主或其他插件的稳定性。

## 2. 核心概念定义

### 2.1 宿主 (Host)

一个没有独立用户界面的核心可执行程序。它是整个应用的基座和能力中心，负责：
*   管理应用的生命周期。
*   解析命令行参数，确定要加载的插件。
*   提供一系列标准化的 **服务 (Service)**。
*   加载、初始化并运行插件，为其提供UI展示的容器（应用外壳）。

### 2.2 插件 (Plugin)

一个独立的动态链接库（DLL），是具体业务功能的实现者。它负责：
*   实现特定的业务逻辑和用户交互界面。
*   通过配置文件声明其基本信息和所需的服务。
*   在宿主提供的环境中运行，并调用宿主提供的服务来完成任务。

### 2.3 服务 (Service)

由宿主实现并提供给插件使用的特定能力接口。服务分为两类：

*   **基础服务 (Basic Services):** 框架默认提供给所有插件的核心服务，无需插件显式声明。例如：日志服务、埋点服务、会员服务等。
*   **可选服务 (Optional Services):** 非必需的、按需使用的服务，插件必须在其配置文件中通过 `dependService` 字段明确声明依赖。例如：OCR识别服务、高级文件处理服务等。

## 3. 详细流程设计

### 3.1 启动流程与多实例架构

本框架采用 **多进程架构** 来支持多实例运行。一个宿主进程只承载一个插件实例。

1.  **命令行解析：** 宿主程序启动后，首先解析命令行传入的参数。
    *   `--plugin-id <id>`: **必需。** 指定要加载的插件ID。
    *   `--instance-id <uuid>`: **可选。** 唯一实例ID，用于支持多实例场景。服务可利用此ID区分不同进程的活动。

2.  **配置文件加载：** 宿主根据 `plugin-id` 查找并加载对应的 `JSON` 配置文件（例如 `idphoto.json`）。
    *   **错误处理：** 若配置文件不存在、JSON格式错误或关键字段（如 `launcherDll`）缺失，宿主将采取 **静默失败** 策略：不显示任何UI，仅在日志中记录详细错误，并终止当前进程。

3.  **服务注册：** 宿主根据插件配置的 `dependService` 列表，初始化并注册所有必需的 **可选服务**。基础服务默认已注册。

### 3.2 插件加载与生命周期

1.  **DLL加载：** 宿主使用Qt Plugin机制加载配置文件中 `launcherDll` 指定的插件DLL。

2.  **服务注入与检查：** 宿主将所有必需的服务（基础+可选）注入到插件实例中。
    *   **快速失败原则：** 在注入阶段，宿主会严格检查所有声明的服务是否都已成功提供。若有任何一个服务缺失，将立即中止插件加载，记录错误日志，并终止进程。

3.  **生命周期调用：** 宿主按以下顺序调用插件实现的生命周期接口：
    *   `initialize()`: 服务注入成功后调用。插件在此进行内部状态初始化。
    *   `getWidget()`: 初始化成功后调用。宿主获取插件返回的 `QWidget`，并将其嵌入到应用外壳的主内容区域进行显示。
    *   `shutdown()`: 在应用关闭前调用。插件在此执行必要的清理工作，如保存用户数据、释放文件句柄等。

## 4. 技术规格

### 4.1 命令行参数

| 参数            | 是否必需 | 描述                                                         |
| --------------- | -------- | ------------------------------------------------------------ |
| `--plugin-id`   | 是       | 插件的唯一标识符，用于定位配置文件。                         |
| `--instance-id` | 否       | 运行实例的唯一ID，用于多实例场景下的区分。                   |

### 4.2 配置文件 (JSON) 规范

每个插件都必须有一个同名的JSON配置文件。

```json
{
    "baseInfo": {
        "appId": "idphoto",
        "appName": "证件照",
        "buildVersion": 1
    },
    "runEnvInfo": {
        "appClassify": {
            "appType": "plugin",
            "dependService": [
                "ocrService"
            ],
            "launcherDll": "idphoto.dll",
            "launcherType": "native"
        }
    }
}
```

*   `baseInfo.appName`: 将被用作宿主窗口的标题。
*   `runEnvInfo.appClassify.dependService`: 插件依赖的 **可选服务** 列表。基础服务无需在此声明。
*   `runEnvInfo.appClassify.launcherDll`: 插件的入口DLL文件名。

### 4.3 插件接口 (API) 定义

插件必须实现以下C++接口（以纯虚基类的形式定义）：

```cpp
class IPlugin {
public:
    virtual ~IPlugin() {}

    // 1. 初始化插件
    virtual bool initialize() = 0;

    // 2. 获取插件主界面Widget
    virtual QWidget* getWidget() = 0;

    // 3. 退出清理
    virtual void shutdown() = 0;
};
```

### 4.4 UI集成规范

*   **窗口控制：** 宿主提供应用外壳（主窗口）。窗口的标题由插件配置文件的 `appName` 决定。
*   **共享菜单栏：** 宿主提供一个标准的、所有插件共享的顶部菜单栏。当前版本，插件不能修改或向其添加内容。

## 5. 范围边界 (Scope)

明确定义本次迭代需要实现和不需要实现的功能，有助于聚焦开发目标。

### 5.1 包含范围 (In Scope)

*   宿主-插件核心架构的实现。
*   基于命令行的插件加载机制。
*   基础服务与可选服务的注入机制。
*   插件生命周期管理（初始化、获取UI、退出）。
*   通过多进程实现的多实例支持。
*   统一的应用外壳和共享菜单栏。

### 5.2 排除范围 (Out of Scope)

*   **服务版本管理：** 所有插件与宿主共享同一版本的服务接口。
*   **插件间直接通信：** 插件之间完全隔离，不能直接调用。
*   **宿主到插件的事件广播：** 不提供宿主主动向插件推送事件的机制。
*   **插件对共享UI的定制：** 插件不能修改共享菜单栏。
*   **一个进程内加载多个插件实例。**
