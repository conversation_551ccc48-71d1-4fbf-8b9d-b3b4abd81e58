#include "HostApplication.h"
#include <QDebug>
#include <QCoreApplication>
#include <QApplication>
#include <QWidget>
#include <QVBoxLayout>
#include <QMenuBar>
#include <QJsonObject>

HostApplication::HostApplication(const QStringList& arguments, QObject* parent)
    : QObject(parent)
    , m_arguments(arguments)
    , m_currentPlugin(nullptr)
{
    qDebug() << "HostApplication created";

    // 连接应用程序退出信号
    connect(QCoreApplication::instance(), &QCoreApplication::aboutToQuit,
            this, &HostApplication::onAboutToQuit);
    qDebug() << "Connected to aboutToQuit signal";
}

HostApplication::~HostApplication()
{
    qDebug() << "HostApplication destructor called";
    cleanupPlugin();
    qDebug() << "HostApplication destroyed";
}

int HostApplication::run()
{
    qDebug() << "HostApplication starting...";
    
    // 1. 解析命令行参数
    if (!parseCommandLine()) {
        return 1; // 解析失败，退出
    }
    
    // 2. 初始化管理器
    initializeManagers();

    // 3. 创建主窗口
    createMainWindow();

    // 4. 加载插件
    qDebug() << QString("Loading plugin: %1").arg(m_pluginId);
    m_currentPlugin = m_pluginManager->loadPlugin(m_pluginId);

    if (!m_currentPlugin) {
        qCritical() << QString("Failed to load plugin: %1").arg(m_pluginId);
        return 1;
    }

    // 5. 获取插件UI并集成到主窗口
    QWidget* pluginWidget = m_currentPlugin->getWidget();
    if (!pluginWidget) {
        qCritical() << "Plugin did not provide a valid widget";
        return 1;
    }

    // 6. 设置插件UI为主窗口的中央控件
    m_mainWindow->setCentralWidget(pluginWidget);

    // 7. 设置窗口标题（从插件配置获取）
    const QJsonObject& config = m_pluginManager->getLastLoadedPluginConfig();
    QJsonObject baseInfo = config["baseInfo"].toObject();
    QString appName = baseInfo["appName"].toString();
    if (appName.isEmpty()) {
        appName = m_pluginId;
    }
    m_mainWindow->setWindowTitle(appName);

    // 8. 显示主窗口
    m_mainWindow->show();

    qDebug() << "Plugin loaded and UI integrated successfully";

    // 9. 启动事件循环
    return QApplication::exec();
}

bool HostApplication::parseCommandLine()
{
    qDebug() << "Parsing command line arguments...";
    
    // 显示所有参数用于调试
    for (int i = 0; i < m_arguments.size(); ++i) {
        qDebug() << QString("Arg[%1]: %2").arg(i).arg(m_arguments[i]);
    }
    
    // 查找 --plugin-id 参数
    bool pluginIdFound = false;
    for (int i = 0; i < m_arguments.size() - 1; ++i) {
        if (m_arguments[i] == "--plugin-id") {
            m_pluginId = m_arguments[i + 1];
            pluginIdFound = true;
            qDebug() << QString("Plugin ID: %1").arg(m_pluginId);
            break;
        }
    }
    
    if (!pluginIdFound) {
        showErrorAndExit("Missing required parameter: --plugin-id");
        return false;
    }
    
    // 查找 --instance-id 参数（可选）
    for (int i = 0; i < m_arguments.size() - 1; ++i) {
        if (m_arguments[i] == "--instance-id") {
            m_instanceId = m_arguments[i + 1];
            qDebug() << QString("Instance ID: %1").arg(m_instanceId);
            break;
        }
    }
    
    // 检查帮助参数
    if (m_arguments.contains("--help") || m_arguments.contains("-h")) {
        showHelp();
        return false;
    }
    
    qDebug() << "Command line parsing completed successfully";
    return true;
}

void HostApplication::initializeManagers()
{
    qDebug() << "Initializing managers...";
    
    // 创建服务管理器
    m_serviceManager = std::make_unique<ServiceManager>();
    
    // 创建插件管理器
    m_pluginManager = std::make_unique<PluginManager>(*m_serviceManager);
    
    qDebug() << "Managers initialized successfully";
}

void HostApplication::createMainWindow()
{
    qDebug() << "Creating main window...";

    // 创建主窗口
    m_mainWindow = std::make_unique<MainWindow>();

    // 连接窗口关闭信号
    connect(m_mainWindow.get(), &MainWindow::aboutToClose,
            this, &HostApplication::cleanupPlugin);

    // 设置窗口基本属性
    m_mainWindow->setMinimumSize(800, 600);
    m_mainWindow->resize(1024, 768);

    // 创建菜单栏（共享菜单栏）
    QMenuBar* menuBar = m_mainWindow->menuBar();

    // 添加基本菜单
    QMenu* fileMenu = menuBar->addMenu("文件(&F)");
    QAction* exitAction = fileMenu->addAction("退出(&X)");
    QObject::connect(exitAction, &QAction::triggered, [this]() {
        qDebug() << "Exit action triggered - closing application";
        QApplication::quit();
    });

    QMenu* helpMenu = menuBar->addMenu("帮助(&H)");
    QAction* aboutAction = helpMenu->addAction("关于(&A)");
    QObject::connect(aboutAction, &QAction::triggered, [this]() {
        // 简单的关于对话框
        qDebug() << "cfhost - Plugin Host Framework v1.0";
    });

    qDebug() << "Main window created successfully";
}

void HostApplication::showHelp() const
{
    qDebug() << "cfhost - Plugin Host Framework";
    qDebug() << "";
    qDebug() << "Usage:";
    qDebug() << "  cfhost --plugin-id <plugin_id> [--instance-id <instance_id>]";
    qDebug() << "";
    qDebug() << "Parameters:";
    qDebug() << "  --plugin-id <plugin_id>     Required. Plugin identifier to load";
    qDebug() << "  --instance-id <instance_id> Optional. Unique instance identifier";
    qDebug() << "  --help, -h                  Show this help message";
    qDebug() << "";
    qDebug() << "Examples:";
    qDebug() << "  cfhost --plugin-id idphoto";
    qDebug() << "  cfhost --plugin-id idphoto --instance-id 12345";
}

void HostApplication::showErrorAndExit(const QString& errorMessage) const
{
    qCritical() << QString("Error: %1").arg(errorMessage);
    qDebug() << "";
    qDebug() << "Use --help for usage information";
}

void HostApplication::onAboutToQuit()
{
    qDebug() << "Application is about to quit - performing cleanup...";
    cleanupPlugin();
}

void HostApplication::cleanupPlugin()
{
    if (m_currentPlugin) {
        qDebug() << "Cleaning up current plugin...";

        // 调用插件的shutdown方法
        m_currentPlugin->shutdown();

        // 通过PluginManager卸载插件
        if (m_pluginManager) {
            m_pluginManager->unloadPlugin(m_currentPlugin);
        }

        m_currentPlugin = nullptr;
        qDebug() << "Plugin cleanup completed";
    } else {
        qDebug() << "No plugin to cleanup";
    }
}

#include "HostApplication.moc"
