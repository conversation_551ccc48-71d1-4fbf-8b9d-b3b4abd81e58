#pragma once

#include <QObject>
#include <QWidget>
#include "IServiceProvider.h"

/**
 * @brief 插件接口
 * 
 * 所有插件必须实现此接口。插件通过此接口与宿主进行交互，
 * 获取服务并提供用户界面。
 */
class IPlugin
{
public:
    virtual ~IPlugin() = default;

    /**
     * @brief 设置服务提供者
     * @param provider 服务提供者实例
     * 
     * 此方法由宿主调用，用于向插件注入服务提供者。
     * 提供默认实现，插件通常无需重写此方法。
     */
    virtual void setServiceProvider(IServiceProvider* provider)
    {
        m_serviceProvider = provider;
    }

    /**
     * @brief 初始化插件
     * @return bool true表示初始化成功，false表示失败
     * 
     * 在服务注入完成后调用。插件应在此方法中进行内部状态初始化，
     * 并可通过m_serviceProvider获取所需服务。
     */
    virtual bool initialize() = 0;

    /**
     * @brief 获取插件主界面
     * @return QWidget* 插件的主用户界面控件
     * 
     * 在初始化成功后调用。返回的QWidget将被嵌入到宿主的主窗口中。
     */
    virtual QWidget* getWidget() = 0;

    /**
     * @brief 关闭插件
     * 
     * 在应用关闭前调用。插件应在此方法中执行必要的清理工作，
     * 如保存用户数据、释放资源等。
     */
    virtual void shutdown() = 0;

protected:
    /**
     * @brief 服务提供者实例
     * 
     * 插件可通过此成员访问宿主提供的各种服务。
     */
    IServiceProvider* m_serviceProvider = nullptr;
};

// Qt插件系统需要的宏声明
Q_DECLARE_INTERFACE(IPlugin, "com.cfpixelengine.IPlugin/1.0")
