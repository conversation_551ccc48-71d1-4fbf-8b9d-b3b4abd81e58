#include "MainWindow.h"
#include <QDebug>
#include <QApplication>

MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent)
{
    qDebug() << "MainWindow created";
}

void MainWindow::closeEvent(QCloseEvent* event)
{
    qDebug() << "MainWindow closeEvent triggered";
    
    // 发出即将关闭信号，让应用程序有机会清理
    emit aboutToClose();
    
    // 接受关闭事件
    event->accept();
    
    // 退出应用程序
    QApplication::quit();
    
    qDebug() << "MainWindow close event processed";
}
