#pragma once

#include <unordered_map>
#include <typeindex>
#include <memory>
#include "IServiceProvider.h"
#include "IService.h"

/**
 * @brief 服务提供者的具体实现
 * 
 * 管理服务实例的生命周期，为插件提供类型安全的服务访问。
 */
class ServiceProvider : public IServiceProvider
{
public:
    ServiceProvider();
    virtual ~ServiceProvider();

    /**
     * @brief 注册服务实例
     * @tparam T 服务接口类型
     * @param service 服务实例的智能指针
     */
    template<typename T>
    void registerService(std::shared_ptr<T> service)
    {
        static_assert(std::is_base_of_v<IService, T>, "T must inherit from IService");
        m_services[std::type_index(typeid(T))] = service;
    }

    /**
     * @brief 注册服务实例（原始指针版本，ServiceProvider将管理其生命周期）
     * @tparam T 服务接口类型
     * @param service 服务实例指针
     */
    template<typename T>
    void registerService(T* service)
    {
        static_assert(std::is_base_of_v<IService, T>, "T must inherit from IService");
        m_services[std::type_index(typeid(T))] = std::shared_ptr<T>(service);
    }

protected:
    // IServiceProvider接口实现
    IService* getServiceImpl(const std::type_info& type) override;

private:
    /**
     * @brief 服务实例映射表
     * 
     * 键：服务类型索引
     * 值：服务实例的智能指针
     */
    std::unordered_map<std::type_index, std::shared_ptr<IService>> m_services;
};
